from maix import image, display, app, time, camera
import cv2
import numpy as np
import math
import struct
from micu_uart_lib import (
    SimpleUART, micu_printf
)

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lower_purple = np.array([130, 80, 80])
        upper_purple = np.array([160, 255, 255])
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []
        
        for cnt in contours_purple:
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            laser_points.append((cx, cy))
            cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        return img, laser_points

# --------------------------- 圆形轨迹点生成函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """在校正后的矩形内生成圆形轨迹点"""
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

# --------------------------- 串口控制命令处理函数 ---------------------------
def check_control_command(uart):
    """检查并处理控制命令

    Returns:
        bool or None: True表示开始处理，False表示停止处理，None表示无命令
    """
    try:
        # 使用底层串口直接读取二进制数据
        data = uart.serial.read()
        if data:
            for byte in data:
                if byte == 0xFF:  # 停止命令
                    return False
                elif byte == 0x00:  # 开始命令
                    return True
    except Exception as e:
        pass  # 静默处理错误

    return None  # 无命令，保持当前状态

# --------------------------- 二进制数据包发送函数 ---------------------------
def send_coordinate_packet(uart, coordinate, coord_type, identifier=0x01):
    """发送坐标数据包

    数据包格式：0x78 + 标识符 + x高字节 + x低字节 + y高字节 + y低字节 + 0xFC

    Args:
        uart: 串口对象
        coordinate: 坐标 (x, y)
        coord_type: 坐标类型 (0x01=激光点, 0x02=圆心)
        identifier: 数据包标识符，默认0x01
    """
    try:
        # 提取坐标值
        x, y = coordinate

        # 限制在16位无符号整数范围内 (0-65535)
        x = max(0, min(65535, int(x)))
        y = max(0, min(65535, int(y)))

        # 构造数据包：0x78, 标识符, x高字节, x低字节, y高字节, y低字节, 0xFC
        packet = struct.pack('>BBHHB', 0x78, coord_type, x, y, 0xFC)

        # 发送二进制数据
        uart.serial.write(packet)

        return True

    except Exception as e:
        return False

# --------------------------- 圆心坐标获取函数 ---------------------------
def get_circle_center(inner_quads):
    """获取圆心坐标

    Args:
        inner_quads: 内框列表，每个元素为(approx, area)

    Returns:
        tuple or None: 圆心坐标(cx, cy)，如果无内框则返回None
    """
    if not inner_quads:
        return None

    # 使用第一个内框作为参考
    approx, area = inner_quads[0]
    M_moments = cv2.moments(approx)
    if M_moments['m00'] != 0:
        cx = int(M_moments['m10'] / M_moments['m00'])
        cy = int(M_moments['m01'] / M_moments['m00'])
        return (cx, cy)
    return None

# --------------------------- 透视变换工具函数 ---------------------------
def perspective_transform(pts, target_width, target_height):
    """
    对四边形进行透视变换
    :param pts: 四边形顶点坐标 (4,2)
    :param target_width: 校正后宽度
    :param target_height: 校正后高度
    :return: 变换矩阵M和逆矩阵M_inv
    """
    # 顶点排序（左上→右上→右下→左下）
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)  # 逆矩阵用于映射回原图
    return M, M_inv, src_pts

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    laser_detector = PurpleLaserDetector()
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        uart.set_frame("", "", False)  # 禁用帧格式以支持二进制传输
    else:
        exit()

    # 核心参数
    min_contour_area = 1000
    max_contour_area = 40000
    target_sides = 4
    
    # 透视变换与圆形参数
    corrected_width = 200    # 校正后矩形宽度
    corrected_height = 150   # 校正后矩形高度
    circle_radius = 40       # 校正后矩形内圆的半径
    circle_num_points = 12   # 圆周点数量

    # 处理控制状态
    processing_enabled = True  # 默认启用处理

    # 系统状态监控变量
    packet_count = 0  # 发送的数据包数量
    command_count = 0  # 接收的控制命令数量
    last_status_time = time.ticks_ms()  # 上次状态切换时间
    status_report_interval = 10000  # 状态报告间隔(ms)
    last_report_time = time.ticks_ms()

    # 性能优化：预计算圆形轨迹点
    corrected_center = (corrected_width//2, corrected_height//2)
    precomputed_circle = generate_circle_points(corrected_center, circle_radius, circle_num_points)

    while not app.need_exit():
        # 检查控制命令
        cmd_result = check_control_command(uart)
        if cmd_result is not None:
            processing_enabled = cmd_result
            command_count += 1
            last_status_time = time.ticks_ms()

        # 如果处理被禁用，跳过图像处理
        if not processing_enabled:
            time.sleep_ms(10)  # 短暂休眠避免占用过多CPU
            continue

        # 读取图像
        img = cam.read()
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        # 直接使用原图，减少内存复制
        output = img_cv

        # 1. 矩形检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 112, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                # 预计算周长，避免重复计算
                perimeter = cv2.arcLength(cnt, True)
                epsilon = 0.02 * perimeter
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))
                    # 限制检测数量，提升性能
                    if len(quads) >= 10:  # 最多处理10个矩形
                        break

        # 按面积排序（外框在前，内框在后）
        quads.sort(key=lambda x: x[1], reverse=True)
        inner_quads = quads[1:]  # 只处理内框

        # 2. 处理内框：透视变换→画圆→映射回原图
        all_circle_points = []  # 存储所有映射回原图的圆轨迹点

        # 获取圆心坐标用于误差计算
        circle_center = get_circle_center(inner_quads)

        for approx, area in inner_quads:
            # 提取顶点
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 计算透视变换矩阵
            M, M_inv, src_pts = perspective_transform(
                pts, corrected_width, corrected_height
            )
            
            # 使用预计算的圆形轨迹点
            corrected_circle = precomputed_circle
            
            # 将校正后的圆轨迹点映射回原图
            if M_inv is not None:
                # 优化：直接使用预计算的numpy数组格式
                corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                # 优化：使用numpy操作代替列表推导
                original_points_int = original_points.astype(np.int32)
                all_circle_points.extend([(int(x), int(y)) for x, y in original_points_int])
                
                # 优化：批量绘制轨迹点（减少函数调用）
                for i in range(0, len(original_points_int), 2):  # 每隔一个点绘制，减少绘制量
                    x, y = original_points_int[i]
                    cv2.circle(output, (int(x), int(y)), 2, (0, 0, 255), -1)
            
            # 绘制内框轮廓（调试用）
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

        # 绘制圆心（调试用）
        if circle_center:
            cv2.circle(output, circle_center, 5, (255, 0, 0), -1)

        # 3. 激光检测
        output, laser_points = laser_detector.detect(output)

        # 4. 串口发送数据
        # 发送激光点与圆心的误差数据包
        if laser_points and circle_center:
            for laser_point in laser_points:
                if send_error_packet(uart, laser_point, circle_center):
                    packet_count += 1

        # 定期状态报告（已禁用打印）
        current_time = time.ticks_ms()
        if current_time - last_report_time > status_report_interval:
            last_report_time = current_time

        # 显示图像（优化：减少不必要的转换）
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)

        # 性能优化：减少CPU占用，但不影响响应性
        time.sleep_ms(1)